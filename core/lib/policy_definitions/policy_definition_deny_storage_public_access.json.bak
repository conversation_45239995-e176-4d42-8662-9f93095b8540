{"name": "Deny-Storage-Public-Access", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "properties": {"displayName": "Storage account public access should be disallowed", "description": "This policy denies the creation of storage accounts with public blob access enabled. Anonymous public read access to containers and blobs in Azure Storage is a convenient way to share data but might present security risks. To prevent data breaches caused by undesired anonymous access, Microsoft recommends preventing public access to a storage account unless your scenario requires it.", "policyType": "Custom", "mode": "All", "metadata": {"category": "Storage", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"field": "Microsoft.Storage/storageAccounts/allowBlobPublicAccess", "exists": "false"}, {"field": "Microsoft.Storage/storageAccounts/allowBlobPublicAccess", "equals": "true"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}
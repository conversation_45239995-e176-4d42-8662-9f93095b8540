{"name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Objects-Not-Recoverable", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "properties": {"displayName": "Key Vault objects should be recoverable", "description": "This policy denies creation of Key Vaults that do not have both soft delete and purge protection enabled. Key Vault objects should be recoverable to prevent accidental or malicious deletion of keys, secrets, and certificates. Soft delete allows recovery of deleted objects within the retention period, while purge protection prevents permanent deletion during the retention period.", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "<PERSON>", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"anyOf": [{"field": "Microsoft.KeyVault/vaults/enableSoftDelete", "exists": "false"}, {"field": "Microsoft.KeyVault/vaults/enableSoftDelete", "notEquals": "true"}, {"field": "Microsoft.KeyVault/vaults/enablePurgeProtection", "exists": "false"}, {"field": "Microsoft.KeyVault/vaults/enablePurgeProtection", "notEquals": "true"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}
{"name": "Deny-VM-Internet-Access-SSH-RDP", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "properties": {"displayName": "Block SSH/RDP access from Internet to Virtual Machines", "description": "This policy prevents direct SSH/RDP access from the Internet to virtual machines by denying: 1) NSG rules that allow SSH/RDP from Internet, and 2) Network interfaces with public IP addresses. This enforces secure access through Azure Bastion, VPN, or private connectivity.", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Compute", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}}, "ports": {"type": "Array", "defaultValue": ["22", "3389"], "metadata": {"displayName": "Blocked Ports", "description": "List of management ports to block from Internet access (default: SSH=22, RDP=3389)"}}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": "[parameters('ports')]"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "equals": "*"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRanges[*]", "where": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRanges[*]", "in": "[parameters('ports')]"}}, "greater": 0}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "0.0.0.0/0"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "where": {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "0.0.0.0/0"}]}}, "greater": 0}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*]", "where": {"allOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRange", "in": "[parameters('ports')]"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRange", "equals": "*"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRanges[*]", "where": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRanges[*]", "in": "[parameters('ports')]"}}, "greater": 0}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "0.0.0.0/0"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "where": {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "0.0.0.0/0"}]}}, "greater": 0}]}]}}, "greater": 0}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"count": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*]", "where": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*].publicIPAddress.id", "exists": "true"}}, "greater": 0}]}]}, "then": {"effect": "[parameters('effect')]"}}}}
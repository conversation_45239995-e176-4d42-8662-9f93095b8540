{"name": "block_public_ip_assignment_to_vms", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Network interfaces should not have public IPs", "policyType": "Custom", "mode": "Indexed", "description": "This policy denies the network interfaces which are configured with any public IP. Public IP addresses allow internet resources to communicate inbound to Azure resources, and Azure resources to communicate outbound to the internet. This should be reviewed by the network security team.", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"not": {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "notLike": "*"}}]}, "then": {"effect": "deny"}}}}
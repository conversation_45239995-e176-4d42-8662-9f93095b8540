{"name": "require_mandatory_tags", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Require mandatory tags on resources", "description": "Resources must include tags", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"Owner": {"type": "String", "metadata": {"displayName": "Owner <PERSON>", "description": "Email or name of the owner"}}, "org": {"type": "String", "metadata": {"displayName": "Org Tag", "description": "Name of the organization"}}, "create_by": {"type": "String", "metadata": {"displayName": "Create by", "description": "Name of the creator"}}, "operation_team": {"type": "String", "metadata": {"displayName": "Operation Team", "description": "Name of the operation team"}}, "project_name": {"type": "String", "metadata": {"displayName": "Project Name", "description": "Name of the project"}}, "env": {"type": "String", "metadata": {"displayName": "Environment", "description": "Environment name"}}, "app_name": {"type": "String", "metadata": {"displayName": "App Name", "description": "Name of the application"}}, "resources_type": {"type": "String", "metadata": {"displayName": "Resources Type", "description": "Resource classification"}}, "priority": {"type": "String", "metadata": {"displayName": "Priority", "description": "Classify the importance of the application (according to the application classification decision)"}}, "data_zone": {"type": "String", "metadata": {"displayName": "Data Zone", "description": "Classify resources as internal or external"}}}, "policyRule": {"if": {"anyOf": [{"field": "[concat('tags[', parameters('Owner'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('org'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('create_by'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('operation_team'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('project_name'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('env'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('app_name'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('resources_type'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('priority'), ']')]", "exists": "false"}, {"field": "[concat('tags[', parameters('data_zone'), ']')]", "exists": "false"}]}, "then": {"effect": "<PERSON><PERSON>"}}}}
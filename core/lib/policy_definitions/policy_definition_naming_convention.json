{"name": "naming_convention", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"policyType": "Custom", "mode": "All", "displayName": "Enforce Naming Convention", "description": "This policy enforces naming conventions for Azure resources.", "metadata": {"version": "1.0.0", "category": "General"}, "parameters": {"orgList": {"type": "Array", "defaultValue": ["ewh", "abc", "xyz"], "metadata": {"displayName": "Organization Codes", "description": "Allowed organization codes."}}, "envList": {"type": "Array", "defaultValue": ["prod", "dev", "test", "stag"], "metadata": {"displayName": "Environment Names", "description": "Allowed environment values."}}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'vm'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[not(equals(length(split(field('name'), '-')[5]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[5], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[5], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions/resourceGroups"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'rg'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[not(equals(length(split(field('name'), '-')[4]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[4], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[4], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"value": "[not(startsWith(toLower(field('name')), 'stg'))]", "equals": true}, {"value": "[less(length(field('name')), 6]", "equals": true}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[contains(toLower(field('name')), current('env'))]", "equals": true}}, "less": 1}, {"value": "[not(greaterOrEquals(substring(field('name'), sub(length(field('name')), 2), 2), '01'))]", "equals": true}, {"value": "[not(lessOrEquals(substring(field('name'), sub(length(field('name')), 2), 2), '98'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Management/managementGroups"}, {"value": "[not(startsWith(toLower(field('name')), 'mg-'))]", "equals": true}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sub'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[not(equals(length(split(field('name'), '-')[4]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[4], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[4], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'app'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[not(equals(length(split(field('name'), '-')[4]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[4], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[4], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sql'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[less(length(split(field('name'), '-')[4]), 3)]", "equals": true}, {"value": "[not(equals(length(split(field('name'), '-')[5]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[5], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[5], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'mysql'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}, {"value": "[less(length(split(field('name'), '-')[4]), 3)]", "equals": true}, {"value": "[not(equals(length(split(field('name'), '-')[5]), 2))]", "equals": true}, {"value": "[lessOrEquals(split(field('name'), '-')[5], '00')]", "equals": true}, {"value": "[greaterOrEquals(split(field('name'), '-')[5], '99')]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'kv'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.ContainerService/managedClusters"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'aks'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')[1]), 3)]", "equals": true}, {"count": {"value": "[parameters('orgList')]", "name": "org", "where": {"value": "[equals(toLower(split(field('name'), '-')[2]), current('org'))]", "equals": true}}, "less": 1}, {"count": {"value": "[parameters('envList')]", "name": "env", "where": {"value": "[equals(toLower(split(field('name'), '-')[3]), current('env'))]", "equals": true}}, "less": 1}]}]}]}, "then": {"effect": "deny"}}}}
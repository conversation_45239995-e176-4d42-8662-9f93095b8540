{"name": "Enforce-Naming", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy enforces naming conventions for Azure resources according to organizational standards.", "displayName": "Enforce Naming Convention", "notScopes": [], "parameters": {"orgList": {"value": ["ewh", "abc", "xyz"]}, "envList": {"value": ["prod", "dev", "test", "stag"]}}, "policyDefinitionId": "${current_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/naming_convention", "nonComplianceMessages": [{"message": "Resources must follow the organizational naming convention standards."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}